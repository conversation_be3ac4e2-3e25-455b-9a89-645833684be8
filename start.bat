@echo off
echo ========================================
echo 鼠标自动化脚本启动器
echo ========================================
echo.
echo 请选择要启动的版本:
echo 1. 完整版 (需要安装所有依赖库)
echo 2. 简化版 (只需要基本库)
echo.
set /p choice="请输入选择 (1 或 2): "

if "%choice%"=="1" (
    echo.
    echo 正在启动完整版...
    python mouse_automation.py
) else if "%choice%"=="2" (
    echo.
    echo 正在启动简化版...
    python mouse_automation_simple.py
) else (
    echo.
    echo 无效选择，启动简化版...
    python mouse_automation_simple.py
)

echo.
pause
