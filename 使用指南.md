# 🎯 鼠标自动化脚本 - 快速使用指南

## 🚀 快速开始 (3步搞定)

### 第一步：安装依赖
双击运行 `install.bat`，选择版本：
- **选择 2** (简化版) - 推荐新手，更稳定
- **选择 1** (完整版) - 高级用户，功能更多

### 第二步：启动程序  
双击运行 `start.bat`，选择对应版本启动

### 第三步：开始使用
1. 点击"新建脚本"，输入名称
2. 点击"开始录制"
3. **简化版**: 移动鼠标到目标位置，按空格键标记
4. **完整版**: 直接在目标位置点击鼠标
5. 点击"停止录制"
6. 点击"执行脚本"开始自动化

## 📋 版本对比

| 功能 | 简化版 ⭐推荐 | 完整版 |
|------|-------------|--------|
| 基本录制回放 | ✅ | ✅ |
| 多脚本管理 | ✅ | ✅ |
| 区域限制 | ✅ 手动输入坐标 | ✅ 拖拽选择 |
| 录制方式 | 按空格键标记 | 自动检测点击 |
| 全局快捷键 | ❌ | ✅ F9/F10/ESC |
| 依赖库数量 | 2个 | 7个 |
| 稳定性 | 🟢 高 | 🟡 中等 |
| 适合人群 | 新手/基本需求 | 高级用户 |

## 🎮 简化版详细使用 (推荐)

### 录制脚本
1. 新建脚本并命名
2. (可选) 设置操作区域：点击"选择屏幕区域"，输入坐标
3. 点击"开始录制"
4. 移动鼠标到第一个要点击的位置
5. 按**空格键**标记这个位置
6. 移动到下一个位置，再按空格键
7. 重复步骤5-6，录制所有需要的点击位置
8. 点击"停止录制"

### 执行脚本
1. 在脚本列表中选择要执行的脚本
2. 点击"执行脚本"
3. 程序会自动按顺序点击录制的位置
4. 点击"停止执行"可以随时停止

## ⚙️ 高级设置

### 延迟调整
- **网络延迟**: 设置点击间隔时间，适应网络响应
- **自动检测**: 点击按钮自动测试网络延迟

### 区域限制
- 限制脚本只在指定屏幕区域内操作
- 简化版：手动输入坐标 (x1,y1) 到 (x2,y2)
- 完整版：拖拽选择区域

## 🔧 故障排除

### 常见问题
1. **程序无法启动**
   - 检查是否安装了Python
   - 运行 `install.bat` 安装依赖

2. **录制没有反应**
   - 简化版：确保按的是空格键
   - 完整版：检查是否在选定区域内

3. **执行时点击位置不准**
   - 检查屏幕分辨率是否改变
   - 重新录制脚本

4. **依赖安装失败**
   - 检查网络连接
   - 尝试使用国内镜像源

### 安全提示
- 使用前请确保了解脚本的执行内容
- 建议先在安全环境中测试
- 可以随时按ESC键(完整版)或点击停止按钮紧急停止

## 📁 文件说明

- `mouse_automation_simple.py` - 简化版程序 ⭐
- `mouse_automation.py` - 完整版程序
- `install.bat` - 一键安装脚本
- `start.bat` - 一键启动脚本
- `scripts.json` - 脚本数据(自动生成)

## 💡 使用技巧

1. **录制前规划**: 先在纸上画出要点击的位置顺序
2. **测试小范围**: 先录制2-3个点击测试效果
3. **合理设置延迟**: 根据目标程序的响应速度调整
4. **备份脚本**: 重要脚本可以复制 `scripts.json` 文件备份
5. **分步录制**: 复杂操作可以分成多个小脚本

---
🎉 **祝您使用愉快！如有问题，请检查README.md获取更多详细信息。**
