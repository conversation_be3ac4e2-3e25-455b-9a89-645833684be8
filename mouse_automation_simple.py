import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import pyautogui
import json
import time
import threading
import requests
from datetime import datetime
import os

class SimpleMouseAutomation:
    def __init__(self, root):
        self.root = root
        self.root.title("鼠标自动化脚本 (简化版)")
        self.root.geometry("600x500")
        
        # 初始化变量
        self.scripts = {}
        self.current_script = None
        self.is_recording = False
        self.is_running = False
        self.selected_area = None
        self.network_delay = 0
        
        # 创建GUI
        self.create_gui()
        
        # 加载脚本
        self.load_scripts()
        
    def create_gui(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 脚本管理区域
        script_frame = ttk.LabelFrame(main_frame, text="脚本管理", padding="5")
        script_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # 脚本列表
        self.script_listbox = tk.Listbox(script_frame, height=6)
        self.script_listbox.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        # 脚本操作按钮
        ttk.Button(script_frame, text="新建脚本", command=self.new_script).grid(row=1, column=0, padx=5)
        ttk.Button(script_frame, text="删除脚本", command=self.delete_script).grid(row=1, column=1, padx=5)
        ttk.Button(script_frame, text="重命名", command=self.rename_script).grid(row=1, column=2, padx=5)
        
        # 区域选择
        area_frame = ttk.LabelFrame(main_frame, text="屏幕区域选择", padding="5")
        area_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Button(area_frame, text="选择屏幕区域", command=self.select_screen_area).grid(row=0, column=0, padx=5)
        self.area_label = ttk.Label(area_frame, text="未选择区域")
        self.area_label.grid(row=0, column=1, padx=5)
        
        # 录制控制
        record_frame = ttk.LabelFrame(main_frame, text="录制控制", padding="5")
        record_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        self.record_btn = ttk.Button(record_frame, text="开始录制", command=self.toggle_recording)
        self.record_btn.grid(row=0, column=0, padx=5)
        
        self.status_label = ttk.Label(record_frame, text="状态: 待机")
        self.status_label.grid(row=0, column=1, padx=5)
        
        # 录制说明
        ttk.Label(record_frame, text="录制时按空格键标记点击位置", font=("Arial", 8)).grid(row=1, column=0, columnspan=2, pady=2)
        
        # 执行控制
        execute_frame = ttk.LabelFrame(main_frame, text="执行控制", padding="5")
        execute_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        self.execute_btn = ttk.Button(execute_frame, text="执行脚本", command=self.toggle_execution)
        self.execute_btn.grid(row=0, column=0, padx=5)
        
        # 延迟设置
        delay_frame = ttk.LabelFrame(main_frame, text="延迟设置", padding="5")
        delay_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Label(delay_frame, text="网络延迟(ms):").grid(row=0, column=0)
        self.network_delay_var = tk.StringVar(value="100")
        ttk.Entry(delay_frame, textvariable=self.network_delay_var, width=10).grid(row=0, column=1, padx=5)
        
        ttk.Button(delay_frame, text="自动检测延迟", command=self.auto_detect_delay).grid(row=0, column=2, padx=5)
        
        # 操作说明
        info_frame = ttk.LabelFrame(main_frame, text="操作说明", padding="5")
        info_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        info_text = """1. 创建新脚本
2. 可选择屏幕区域限制操作范围
3. 开始录制，按空格键标记要点击的位置
4. 停止录制，然后执行脚本"""
        
        ttk.Label(info_frame, text=info_text, justify=tk.LEFT).grid(row=0, column=0, sticky=tk.W)
        
    def new_script(self):
        """创建新脚本"""
        name = simpledialog.askstring("新建脚本", "请输入脚本名称:")
        if name and name not in self.scripts:
            self.scripts[name] = {
                'clicks': [],
                'area': None,
                'created': datetime.now().isoformat()
            }
            self.update_script_list()
            self.save_scripts()
            
    def delete_script(self):
        """删除选中的脚本"""
        selection = self.script_listbox.curselection()
        if selection:
            script_name = self.script_listbox.get(selection[0])
            if messagebox.askyesno("确认删除", f"确定要删除脚本 '{script_name}' 吗？"):
                del self.scripts[script_name]
                self.update_script_list()
                self.save_scripts()
                
    def rename_script(self):
        """重命名脚本"""
        selection = self.script_listbox.curselection()
        if selection:
            old_name = self.script_listbox.get(selection[0])
            new_name = simpledialog.askstring("重命名脚本", "请输入新名称:", initialvalue=old_name)
            if new_name and new_name != old_name and new_name not in self.scripts:
                self.scripts[new_name] = self.scripts.pop(old_name)
                self.update_script_list()
                self.save_scripts()
                
    def update_script_list(self):
        """更新脚本列表显示"""
        self.script_listbox.delete(0, tk.END)
        for script_name in self.scripts.keys():
            self.script_listbox.insert(tk.END, script_name)
            
    def select_screen_area(self):
        """选择屏幕区域"""
        messagebox.showinfo("选择区域", "请在弹出的对话框中输入区域坐标")
        
        try:
            x1 = simpledialog.askinteger("区域选择", "请输入左上角X坐标:", minvalue=0)
            if x1 is None:
                return
            y1 = simpledialog.askinteger("区域选择", "请输入左上角Y坐标:", minvalue=0)
            if y1 is None:
                return
            x2 = simpledialog.askinteger("区域选择", "请输入右下角X坐标:", minvalue=x1)
            if x2 is None:
                return
            y2 = simpledialog.askinteger("区域选择", "请输入右下角Y坐标:", minvalue=y1)
            if y2 is None:
                return
                
            self.selected_area = {'x1': x1, 'y1': y1, 'x2': x2, 'y2': y2}
            self.area_label.config(text=f"区域: ({x1},{y1}) - ({x2},{y2})")
            
        except Exception as e:
            messagebox.showerror("错误", f"区域选择失败: {e}")
            
    def toggle_recording(self):
        """切换录制状态"""
        if not self.is_recording:
            selection = self.script_listbox.curselection()
            if not selection:
                messagebox.showwarning("警告", "请先选择一个脚本")
                return
                
            self.current_script = self.script_listbox.get(selection[0])
            self.scripts[self.current_script]['clicks'] = []  # 清空之前的录制
            self.is_recording = True
            self.record_btn.config(text="停止录制")
            self.status_label.config(text="状态: 录制中... (按空格键标记点击)")
            
            # 绑定空格键事件
            self.root.bind('<space>', self.record_click)
            self.root.focus_set()
            
        else:
            self.is_recording = False
            self.record_btn.config(text="开始录制")
            self.status_label.config(text="状态: 录制完成")
            self.root.unbind('<space>')
            self.save_scripts()
            
    def record_click(self, event=None):
        """记录当前鼠标位置作为点击点"""
        if not self.is_recording:
            return
            
        x, y = pyautogui.position()
        
        # 检查是否在选定区域内
        if self.selected_area:
            if not (self.selected_area['x1'] <= x <= self.selected_area['x2'] and 
                   self.selected_area['y1'] <= y <= self.selected_area['y2']):
                messagebox.showwarning("警告", "当前位置不在选定区域内")
                return
        
        # 记录点击位置
        click_data = {
            'x': x,
            'y': y,
            'timestamp': time.time()
        }
        self.scripts[self.current_script]['clicks'].append(click_data)
        print(f"记录点击: ({x}, {y})")
        messagebox.showinfo("录制", f"已记录点击位置: ({x}, {y})")
        
    def toggle_execution(self):
        """切换执行状态"""
        if not self.is_running:
            selection = self.script_listbox.curselection()
            if not selection:
                messagebox.showwarning("警告", "请先选择一个脚本")
                return
                
            script_name = self.script_listbox.get(selection[0])
            if not self.scripts[script_name]['clicks']:
                messagebox.showwarning("警告", "该脚本没有录制的点击")
                return
                
            self.is_running = True
            self.execute_btn.config(text="停止执行")
            self.status_label.config(text="状态: 执行中...")
            
            # 开始执行线程
            self.execute_thread = threading.Thread(target=self.execute_script, args=(script_name,))
            self.execute_thread.daemon = True
            self.execute_thread.start()
        else:
            self.is_running = False
            self.execute_btn.config(text="执行脚本")
            self.status_label.config(text="状态: 执行停止")
            
    def execute_script(self, script_name):
        """执行脚本"""
        clicks = self.scripts[script_name]['clicks']
        network_delay = int(self.network_delay_var.get()) / 1000.0
        
        while self.is_running:
            for i, click in enumerate(clicks):
                if not self.is_running:
                    break
                    
                # 移动并点击
                pyautogui.click(click['x'], click['y'])
                
                # 计算延迟
                if i < len(clicks) - 1:
                    original_delay = clicks[i+1]['timestamp'] - click['timestamp']
                    adjusted_delay = original_delay + network_delay
                    time.sleep(max(0.1, adjusted_delay))
                else:
                    time.sleep(1)  # 循环间隔
                    
    def auto_detect_delay(self):
        """自动检测网络延迟"""
        try:
            start_time = time.time()
            response = requests.get('https://www.baidu.com', timeout=5)
            if response.status_code == 200:
                network_delay = (time.time() - start_time) * 1000
                self.network_delay_var.set(str(int(network_delay)))
                messagebox.showinfo("延迟检测", f"网络延迟: {int(network_delay)}ms")
            else:
                messagebox.showwarning("警告", "网络连接异常")
        except Exception as e:
            messagebox.showerror("错误", f"延迟检测失败: {e}")
            
    def save_scripts(self):
        """保存脚本到文件"""
        try:
            with open('scripts.json', 'w', encoding='utf-8') as f:
                json.dump(self.scripts, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存脚本失败: {e}")
            
    def load_scripts(self):
        """从文件加载脚本"""
        try:
            if os.path.exists('scripts.json'):
                with open('scripts.json', 'r', encoding='utf-8') as f:
                    self.scripts = json.load(f)
                self.update_script_list()
        except Exception as e:
            print(f"加载脚本失败: {e}")
            
    def on_closing(self):
        """程序关闭时的处理"""
        self.is_recording = False
        self.is_running = False
        self.save_scripts()
        self.root.destroy()

def main():
    # 设置pyautogui安全设置
    pyautogui.FAILSAFE = True
    pyautogui.PAUSE = 0.1
    
    root = tk.Tk()
    app = SimpleMouseAutomation(root)
    
    # 绑定关闭事件
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    
    root.mainloop()

if __name__ == "__main__":
    main()
