@echo off
echo ========================================
echo 鼠标自动化脚本 - 依赖安装程序
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境
    echo 请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python环境检查通过
echo.

echo 请选择要安装的版本:
echo 1. 完整版 (安装所有功能库，包括全局快捷键等)
echo 2. 简化版 (只安装基本库，功能简化但更稳定)
echo.
set /p choice="请输入选择 (1 或 2): "

echo.
echo 正在安装依赖包...
echo.

if "%choice%"=="1" (
    echo 安装完整版依赖...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo.
        echo 安装失败，尝试使用国内镜像源...
        pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
    )
) else (
    echo 安装简化版依赖...
    pip install -r requirements_simple.txt
    if errorlevel 1 (
        echo.
        echo 安装失败，尝试使用国内镜像源...
        pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements_simple.txt
    )
)

echo.
echo ========================================
echo 安装完成！
echo ========================================
echo.
echo 现在可以运行 start.bat 来启动程序
echo.
pause
