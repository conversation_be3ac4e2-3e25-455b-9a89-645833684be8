# 鼠标自动化脚本

这是一个功能强大的鼠标自动化工具，可以录制和回放鼠标点击操作。

## 版本说明

### 🚀 完整版 (mouse_automation.py)
- 全局快捷键支持 (F9/F10/ESC)
- 高级屏幕区域选择
- 自动鼠标点击检测
- 图像处理功能
- 需要安装更多依赖库

### 🎯 简化版 (mouse_automation_simple.py) **推荐新手使用**
- 基本的录制和回放功能
- 手动标记点击位置 (按空格键)
- 简单的区域选择
- 只需要基本库，更稳定
- 适合初学者和基本需求

## 功能特点

1. **自定义屏幕区域选择** - 限制自动操作的空间范围
2. **鼠标点击录制** - 记录鼠标点击的位置和时间间隔
3. **快捷键控制** - 使用快捷键或按钮控制
4. **多脚本管理** - 可以存放多个脚本并自定义选择
5. **网络延迟自适应** - 自动检测和适应网络延迟
6. **GUI界面** - 直观的图形用户界面

## 快速开始

### 方法一：使用安装脚本 (推荐)
1. 双击运行 `install.bat` 选择版本并安装依赖
2. 双击运行 `start.bat` 选择版本并启动程序

### 方法二：手动安装
```bash
# 简化版 (推荐)
pip install -r requirements_simple.txt
python mouse_automation_simple.py

# 完整版
pip install -r requirements.txt
python mouse_automation.py
```

## 使用方法

### 简化版使用步骤 (推荐新手)

1. **创建新脚本**
   - 点击"新建脚本"按钮，输入脚本名称

2. **选择屏幕区域**（可选）
   - 点击"选择屏幕区域"按钮
   - 输入区域的坐标范围

3. **录制鼠标点击**
   - 选择一个脚本，点击"开始录制"
   - 将鼠标移动到要点击的位置
   - 按空格键标记该位置为点击点
   - 重复上述步骤录制多个点击位置
   - 点击"停止录制"结束

4. **执行脚本**
   - 选择要执行的脚本
   - 点击"执行脚本"按钮
   - 脚本将自动重复执行录制的点击操作

### 完整版使用步骤

1. **创建新脚本** - 同简化版

2. **选择屏幕区域**（可选）
   - 点击"选择屏幕区域"按钮
   - 在屏幕上拖拽选择要操作的区域

3. **录制鼠标点击**
   - 选择一个脚本
   - 点击"开始录制"或按F9键
   - 在目标区域进行实际的鼠标点击操作
   - 再次点击"停止录制"或按F9键结束录制

4. **执行脚本**
   - 选择要执行的脚本
   - 点击"执行脚本"或按F10键
   - 脚本将自动重复执行录制的点击操作

### 通用功能

- **调整延迟**: 手动设置网络延迟时间或点击"自动检测延迟"按钮
- **脚本管理**: 可以删除、重命名脚本

## 快捷键

### 完整版快捷键
- **F9**: 开始/停止录制
- **F10**: 开始/停止执行
- **ESC**: 紧急停止所有操作

### 简化版快捷键
- **空格键**: 录制时标记当前鼠标位置为点击点
- 其他操作通过界面按钮完成

## 注意事项

1. 请确保在安全的环境中使用此工具
2. 录制时请确保鼠标点击准确
3. 执行脚本时请注意不要干扰其他程序
4. 使用ESC键可以紧急停止所有操作
5. 脚本数据会自动保存到scripts.json文件中

## 文件说明

### 程序文件
- `mouse_automation.py` - 完整版主程序
- `mouse_automation_simple.py` - 简化版主程序 (推荐)
- `mouse_recorder.py` - 高级鼠标录制模块

### 配置文件
- `requirements.txt` - 完整版依赖包列表
- `requirements_simple.txt` - 简化版依赖包列表
- `config.json` - 程序配置文件
- `scripts.json` - 脚本数据存储文件（自动生成）

### 启动脚本
- `install.bat` - 依赖安装脚本
- `start.bat` - 程序启动脚本
- `README.md` - 使用说明文档

## 故障排除

如果遇到问题，请检查：
1. 是否正确安装了所有依赖包
2. 是否有足够的系统权限
3. 防火墙是否阻止了网络延迟检测功能
